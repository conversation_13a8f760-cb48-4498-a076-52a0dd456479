<!DOCTYPE html>
<html lang="ko" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title th:text="${company != null} ? ${company.siteName}">#{여행사}</title>
    <head th:replace="~{front/inc/common-header :: include}" />
	<head th:replace="~{front/inc/header :: header-script}" />
</head>

<body>
	<header th:replace="~{front/inc/header :: header}" />

	<div class="sub-wrap"
		id="terms-page"
		th:object="${product}"
		th:with="imageList = ${__${#strings.concat('imageList')}__}">
		<div class="contents">
			<div class="reserve_view_wrap">
				<div class="h4-title">
					<h4>예약 전, 반드시 숙지해주세요!</h4>
					<th:block th:if="${productType eq 'product'}">
						<div class="step_box">
							<div class="step_by_step">
								<div class="step_by_step_circle"></div>
								<div class="step_by_step_line"></div>
							</div>
							<div class="step_by_step step_by_step_last">
								<div class="step_by_step_circle on"></div>
							</div>
						</div>
					</th:block>
					<th:block th:unless="${productType eq 'product'}">
						<div class="step_box">
							<div class="step_by_step">
								<div class="step_by_step_circle"></div>
								<div class="step_by_step_line"></div>
							</div>
							<div class="step_by_step step_by_step_last">
								<div class="step_by_step_circle on"></div>
							</div>
						</div>
					</th:block>
				</div>

				<div class="">
					<div class="before_reservation_final_payment">
						<div class="before_reservation_checklist">
							<div class="line_title_box">
								<div class="line_title_point"></div>
								<p>주의사항</p>
							</div>

							<div class="scroll_chk_box">
								<div class="scroll_box">
									<div class="scroll_contents" th:if="${policyNotice != null}" th:utext="${policyNotice.content}">

									</div>
								</div>
								<div class="argee_secession">
									<div class="agree">
										<input type="checkbox" id="agree_chk_11">
										<label for="agree_chk_11" class="agree_chk"></label>
										<p class="agree_chk_txt">
											주의사항을 숙지하였습니다.
										</p>
									</div>
								</div><!--argee_secession-->
							</div>

							<div class="line_title_box">
								<div class="line_title_point"></div>
								<p>강화유니버스 약속문</p>
							</div>

							<div class="scroll_chk_box">
								<div class="scroll_box">
									<div class="scroll_contents policy_terms" >
										<th:block th:if="${policyUniverseTerms != null}" th:utext="${policyUniverseTerms.content}">

										</th:block>
									</div>
								</div>
								<div class="argee_secession">
									<div class="agree">
										<input type="checkbox" id="agree_chk_12">
										<label for="agree_chk_12" class="agree_chk"></label>
										<p class="agree_chk_txt">
											강화유니버스 약속문을 실천하겠습니다.
										</p>
									</div>
								</div><!--argee_secession-->
							</div>

							<div class="line_title_box">
								<div class="line_title_point"></div>
								<p>개인정보 이용약관</p>
							</div>

							<div class="scroll_chk_box">
								<div class="scroll_box">
									<div class="scroll_contents">
										<th:block th:if="${policyPrivacy != null}" th:utext="${policyPrivacy.content}">

										</th:block>
									</div>
								</div>
								<div class="argee_secession">
									<div class="agree">
										<input type="checkbox" id="agree_chk_13">
										<label for="agree_chk_13" class="agree_chk"></label>
										<p class="agree_chk_txt">
											개인정보 이용약관에 동의합니다.
										</p>
									</div>
								</div><!--argee_secession-->
							</div>
						</div>
						<div class="final_payment_details">
							<div class="final_payment_details_fix">
								<p class="final_payment_details_title">최종 결제 내역</p>
								<div class="final_payment_details_img_info">
									<div class="final_payment_details_img">
										<img th:src="@{/upload/product/{url}(url=${imageList[0].uploadFilename})}">
									</div>

									<div class="final_payment_details_txt">
										<th:block th:if="${productType eq 'product'}">
											<p class="stay_info_name stay_info_name_green" th:text="*{categoryTitle}">카테고리</p>
										</th:block>

										<p class="stay_info_room_txt" th:text="*{productTitle}">셋이서 집 짓고 삽니다만_책방시점 강연</p>
										<th:block th:if="${productType eq 'product'}">
											<th:block th:if="${productPrice != null and productPrice[0] != null }">
												<div th:text="|가격 : ${#numbers.formatInteger(productPrice[0], 1, 'COMMA')} 원|">2,523.42</div>
											</th:block>
											<th:block th:unless="${productPrice != null and productPrice[0] != null }">
												<div th:text="|가격문의|"></div>
											</th:block>
										</th:block>
										<th:block th:unless="${productType eq 'product'}">
											<div class="stay_info_room_people" th:text="|${pickPeople}인 예약|">2인 예약</div>
										</th:block>

									</div>
								</div>

								<div class="schedule_final_payment_price">
									<p class="schedule_final_payment_price_title">선택일정</p>
									<div class="schedule_final_payment_price_box">
										<span id="final-pick-date"></span>
									</div>

									<p class="schedule_final_payment_price_title">상품 금액</p>
									<div class="schedule_final_payment_price_box price-area">
										<span th:text="|${pickPeople}인|">2인</span>
										<h5 class="final-price" >144,000<em>원</em></h5>
									</div>

									<div class="schedule_final_payment_price_title use-point-area">
										<div class="title">포인트 사용</div>
										<div class="use-point">
											<span>보유 포인트 : </span>
											<div><p class="remain-point"></p><em>점</em></div>
										</div>
									</div>
									<div class="schedule_final_payment_price_box point-area">
										<input type="text" class="number-format" id="usePoint" name="usePoint" th:placeholder="${userPoint != null and userPoint.pointRemain >= 5000 ? '사용하실 포인트를 입력하세요.' : '사용가능한 포인트가 없습니다.'}" 
										onkeyup="fnUsePointEvent(this)" th:disabled="${userPoint != null and userPoint.pointRemain < 5000}">
										<em>점</em>
									</div>
									<!--/*
									<div style="display: flex;justify-content: space-between;margin-bottom:20px;">
										<span>*사용가능한포인트</span>
										<p ><span class="remain-point"></span><em>점</em></p>
									</div>
									*/-->
									<p class="point-info-text">*포인트 사용은 최소 5000점 부터 사용 가능합니다.</p>

									<p class="schedule_final_payment_price_title">최종 결제 금액</p>
									<div class="schedule_final_payment_price_box total-price">
										<span>합계</span>
										<h5 id="final-price" class="final-price bold">144,000<em>원</em></h5>
									</div>
									<!-- /*
									<p class="schedule_final_payment_price_title">최종 결제 금액 (이전)</p>
									<div class="schedule_final_payment_price_box">
										<span id="final-pick-people">2인</span>
										<span>
											<span >110,000</span> 원
										</span>
									</div>
									*/ -->
								</div>

								<div>
									<!-- 할인 쿠폰 -->
									<!--/*
									<div>
										<input type="checkbox" id="coupon-box" />
										<label for="coupon-box"> 5,000원 쿠폰 적용 </label>
									</div>
									*/-->

									<!-- 결제 UI -->
									<div id="payment-method"></div>
									<!-- 이용약관 UI -->
									<div id="agreement"></div>
									<!-- 결제하기 버튼 -->
									<!--/*
									<button class="button" id="payment-button" style="margin-top: 30px">결제하기</button>
									*/-->
								</div>
								<div class="final_payment_btn" id="payment-button">결제하기</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<footer th:replace="~{front/inc/footer :: footer-bluewind}" />
	<div th:replace="~{front/inc/header :: progressbar-popup}" />

	<script src="https://js.tosspayments.com/v2/standard"></script>
	<script th:inline="javascript">
		var userPoint = [[${userPoint}]];
		var usePoint = 0;
		const originTotalPrice = [[${totalPrice}]];
		var totalPrice = [[${totalPrice}]];;
		$(document).ready(function(){
			var userEmail = [[${session.login.userEmail}]];
			var userName  = [[${session.login.userName}]];
			var userMobile = [[${session.login.userMobile}]];
			var productSerial = [[${product.productSerial}]];
			var productTourId = [[${product.productTourId}]];
			var pickDate = String( [[${pickDate}]] );
			var productPrice = [[${productPrice}]];
			var pickPeople = [[${pickPeople}]];
			var pickDays = [[${pickDays}]]||1;
			var orderId = [[${orderId}]];
			var optionId = [[${optionId}]];
			var optionName = [[${optionName}]];
			var userIslandLifeId = [[${userIslandLifeId}]];

			var consecuriveDiscountAmount = [[${consecuriveDiscountAmount}]];
			var extraPersonDefualtCharge = [[${extraPersonDefualtCharge}]];
			var extraPersonConsecuriveCharge = [[${extraPersonConsecuriveCharge}]];

			var defualtAmount  = [[${defualtAmount}]];
			var discountAmount = [[${discountAmount}]];
			var chargeAmount   = [[${chargeAmount}]];
	
			if( userPoint != null ){
				
				$('.remain-point').text( (userPoint?.pointRemain??0).toLocaleString('ko-KR') );
				$('#usePoint').attr("max", userPoint.pointRemain);

				var useMaxPoint=0;
				if( userPoint.pointRemain >= totalPrice ){
					useMaxPoint = totalPrice; //상품금액 까지 구매가능
				} else {
					useMaxPoint = userPoint.pointRemain; //
				}

				$('.number-format').formatNumber({
				    maxDecimal: 0,       // 소수점 이하 최대 2자리
				    allowDecimal: false,  // 소수점 입력 허용
				    maxValue: useMaxPoint, // 최대값 5000
				    minValue: 0         // 최소값 5000
				});
			}

			// console.log( productTourId );
			// console.log( productSerial );
			/*
			 console.log( pickDate );
			 console.log( userPoint );
			 console.log( pickDays );
			 console.log( `기 본 가격 : ${ defualtAmount }원` );

			 console.log( `연박 할일가 : -${discountAmount}원` );
			 console.log( `추가인원금액 : +${chargeAmount}원` );
			 console.log( '-----------' );
			 console.log( totalPrice );
			*/
			// console.log( pickPeople );
			// console.log( productPrice );
			// console.log( orderId );
			// console.log( optionId );
			// console.log( userIslandLifeId );

			var arrsPicks = pickDate.split(',');
			var pickDateText;
			if( arrsPicks.length > 1 ){
				var startDate = new Date(arrsPicks[0]);
				var endDate = new Date(arrsPicks[arrsPicks.length-1]) ;
				endDate.setDate(endDate.getDate());
				console.log( startDate, endDate.format('yyyy-MM-dd') );
				pickDateText = `${startDate.format('yyyy.MM.dd')} ~ ${endDate.format('yyyy.MM.dd')}, (${pickDays}박 ${pickDays+1}일)`;
			} else {
				pickDateText = pickDate.split(',').map(date => date.replace(/(\d{4})-(\d{2})-(\d{2})/, '$1. $2. $3')).join(' ~ ')
			}

			$('html').scrollTop(0);
			//$('#final-pick-date').text();
			$('#final-pick-date').text(pickDateText);

			$('#final-pick-people').text( [[${pickPeople}]]+'인');
			$('.final-price').text(( totalPrice ).toLocaleString('ko-KR'));

			main();
		});

		async function main() {
			const button = document.getElementById("payment-button");
			const coupon = document.getElementById("coupon-box");
			
			const tPrice = Number([(${totalPrice})]||0);
			
			// ------  결제위젯 초기화 ------
			const clientKey = [[${pgTosspayCKey}]];
			const tossPayments = TossPayments(clientKey);

			// 회원 결제
			//const customerKey = "vaTQl1uW_dtMAA4yiqItU";
			const customerKey = [[${session.login.userEmail}]];
			console.log(clientKey);
			console.log(customerKey);

			// 비회원 결제
			// const widgets = tossPayments.widgets({ customerKey: TossPayments.ANONYMOUS });

			const widgets = tossPayments.widgets({customerKey,});

			// ------ 주문의 결제 금액 설정 ------
			await widgets.setAmount({currency: "KRW",value: [[${totalPrice}]],});

			await Promise.all([
				// ------  결제 UI 렌더링 ------
				widgets.renderPaymentMethods({selector: "#payment-method",variantKey: "DEFAULT",}),
				// ------  이용약관 UI 렌더링 ------
				widgets.renderAgreement({selector: "#agreement", variantKey: "AGREEMENT"}),
			]);

			// ------  주문서의 결제 금액이 변경되었을 경우 결제 금액 업데이트 ------
			/* 일단 주석
			coupon.addEventListener("change", async function () {
				if (coupon.checked) {
					await widgets.setAmount({currency: "KRW",value: 50000 - 5000,});
					return;
				}
				await widgets.setAmount({currency: "KRW",value: [(${totalPrice})],});
			});
			*/

			// ------ '결제하기' 버튼 누르면 결제창 띄우기 ------
			button.addEventListener("click",
				async function () {
					var productType = [[${productType}]];
					const productRunStartTime = [[${productRunStartTime}]];

					// 현재 시간과 productRunStartTime 비교 (분단위까지)
					if (productRunStartTime) {
						const now = new Date();
						const currentTime = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');

						if (currentTime === productRunStartTime) {
							alert('예약 가능시간이 아닙니다.');
							return;
						}
					}

					if ( productType != 'stay' && productType == 'product' ) {
						return;
					}

					usePoint = Number( numberWithUncomma($('#usePoint').val()) ) ;

					if( usePoint != 0 && usePoint < 5000){
						alert("사용포인트가 최소 5000점 이상 이여야 됩니다.");
						return;
					}

					if ( !($("#agree_chk_11").is(":checked") && $("#agree_chk_12").is(":checked") && $("#agree_chk_13").is(":checked")) ) {
						alert("약관에 동의해주세요.");
						if (!$("#agree_chk_11").is(":checked")) {
							$("label[for='agree_chk_11']").attr("tabindex", -1).focus();
						} else if (!$("#agree_chk_12").is(":checked")) {
							$("label[for='agree_chk_12']").attr("tabindex", -1).focus();
						} else if (!$("#agree_chk_13").is(":checked")) {
							$("label[for='agree_chk_13']").attr("tabindex", -1).focus();
						}
						return;
					}

					var userEmail = [[${session.login.userEmail}]];
					var userName  = [[${session.login.userName}]];
					var userMobile = [[${session.login.userMobile}]];
					var productSerial = [[${product.productSerial}]];
					var productTourId = [[${product.productTourId}]];
					var pickDate = String( [[${pickDate}]] );
					var productPrice = [[${productPrice}]];
					var totalPrice = [[${totalPrice}]];
					var pickPeople = [[${pickPeople}]];
					var pickDays = [[${pickDays}]]||1;
					var orderId = [[${orderId}]];
					var optionId = [[${optionId}]];
					var optionName = [[${optionName}]];
					var optionOneCode = [[${optionOneCode}]];
					var pickCapacity = [[${pickCapacity}]];
					var userIslandLifeId = [[${userIslandLifeId}]];

					var consecuriveDiscountAmount = [[${consecuriveDiscountAmount}]];
					var extraPersonDefualtCharge = [[${extraPersonDefualtCharge}]];
					var extraPersonConsecuriveCharge = [[${extraPersonConsecuriveCharge}]];

					var defualtAmount  = [[${defualtAmount}]];
					var discountAmount = [[${discountAmount}]];
					var chargeAmount   = [[${chargeAmount}]];
					
					if( userMobile == null || userMobile == undefined ) {
						alert('사용자 연락처 정보 미입력시 결제진행이 어렵습니다.\n[마이페이지]-[프로필 설정]-[전화번호] 설정이후 결제진행을 하여주십시오. ');
						retrun ;
					}else if( userMobile.length != 0 && userMobile.indexOf('-') !=0 ){
						userMobile = userMobile.replaceAll('-','');
					}
					/*
					console.log( productTourId );
					console.log( productSerial );
					console.log( pickDate );
					console.log( totalPrice );
					console.log( pickPeople );
					console.log( productPrice );
					console.log( orderId );
					*/

					var params = `productSerial=${productSerial}&productTourId=${productTourId}`;
						params += `&productType=${productType}`;
						params += `&pickDate=${pickDate}`;
						params += `&productPrice=${productPrice}`;
						params += `&pickPeople=${pickPeople}`;
						params += `&pickDays=${pickDays}`;
						params += `&optionId=${optionId}`;
						params += `&optionName=${optionName}`;
						params += `&optionOneCode=${optionOneCode}`;
						params += `&pickCapacity=${pickCapacity}`;
						params += `&userIslandLifeId=${userIslandLifeId}`;
						params += `&usePoint=${usePoint}`;

						params += `&consecuriveDiscountAmount=${consecuriveDiscountAmount}`;
						params += `&extraPersonDefualtCharge=${extraPersonDefualtCharge}`;
						params += `&extraPersonConsecuriveCharge=${extraPersonConsecuriveCharge}`;

						params += `&defualtAmount=${defualtAmount}`;
						params += `&discountAmount=${discountAmount}`;
						params += `&chargeAmount=${chargeAmount}`;

					var realPrice = totalPrice - usePoint;
					//console.log(params);
					//alert(params);
					//alert(realPrice);

					if( realPrice > 0 ){
						
						await widgets.setAmount({currency: "KRW",value: realPrice,});

						await widgets.requestPayment({
							orderId: orderId,
							orderName: [[${product.productTitle}]],
							successUrl: window.location.origin + `/order/process?`+params,
							failUrl: window.location.origin + "/order/fail",
							customerEmail: userEmail,
							customerName: userName,
							customerMobilePhone: userMobile,
						})
						
					} else {
						params += `&amount=${realPrice}`;
						params += `&orderId=`+orderId;
						params += `&paymentType=none`;
						params += `&paymentKey=none`;
						params += `&txTid=none`;
						params += `&status=none`;
						params += `&method=none`;
						params += `&tossPaymentJson=none`;
						
						//console.log(params);
						//alert(params);

						var newForm = $('<form></form>');

						newForm.attr("name","newForm");
						newForm.attr("method","post");
						newForm.attr("action",'/order/process');
						newForm.attr("target","_self");

						//newForm.append($('<input/>', {type: 'hidden', name: 'params', value: params }));
						
						// 문자열을 '&' 기준으로 분리하여 key-value 쌍을 hidden input으로 변환
					    params.split("&").forEach(function(param) {
					        var keyValue = param.split("=");
					        var key = keyValue[0];
					        var value = keyValue[1] || ""; // 값이 없을 경우 빈 문자열 처리
					        newForm.append($('<input/>', { type: 'hidden', name: key, value: value }));
					    });
						
						newForm.append($('<input/>', {type: 'hidden', name: [[${_csrf.parameterName}]], value:[[${_csrf.token}]]} ));

						$(document.body).append(newForm);

						newForm.submit();

					}
				}
			);
		}

		function fnUsePointEvent(target){
			var usePoint = parseInt( numberWithUncomma(  $(target).val()) ) || 0;

			$('#final-price').text(( originTotalPrice - usePoint ).toLocaleString('ko-KR'));
		}

		function fnGoPayment(){
			if ($("#agree_chk_11").is(":checked") && $("#agree_chk_12").is(":checked") && $("#agree_chk_13").is(":checked")) {
				$('#terms-page').hide();
				$('#complete-page').show();
				$('html').scrollTop(0);
				let formattedDate = pickDate;
				if (pickDate.match(/^\d{4}-\d{1,2}-\d{1,2}$/)) {
					const [year, month, day] = pickDate.split('-');
					formattedDate = `${parseInt(month)}월 ${parseInt(day)}일`;
				}
				$("#result-pick-date").text(formattedDate);
				$("#result-pick-people").text(`${pickPeople}인`);
				$("#result-price").text(`${(pickPeople * productPrice ).toLocaleString('ko-KR')}`);
			} else {
				alert("약관에 동의해주세요.");
				if (!$("#agree_chk_11").is(":checked")) {
					$("label[for='agree_chk_11']").attr("tabindex", -1).focus();
				} else if (!$("#agree_chk_12").is(":checked")) {
					$("label[for='agree_chk_12']").attr("tabindex", -1).focus();
				} else if (!$("#agree_chk_13").is(":checked")) {
					$("label[for='agree_chk_13']").attr("tabindex", -1).focus();
				}
			}
		}

	</script>
<!--/**/-->
	<script th:inline="javascript">
		$(document).ready(function(){
			/*
			$('#productSerials').val(searchParams.get('productSerial'));
			$('#productTourIds').val(searchParams.get('productTourId'));
			$('#productPrices').val(searchParams.get('productPrice'));
			$('#orderCount').val(searchParams.get('orderCount'));
			$('#orderDate').val(searchParams.get('orderDate'));
			$('#optionPack').val(searchParams.get('optionPack'));
			$('#orderPage').val(searchParams.get('orderPage'));
			*/

			//console.log( [[${reservation}]]);
		})

		goMoveProduct = function(url){
			window.open(url,'_blank');
		}

		fnMoveCompelte = function(id){
			var newForm = $('<form></form>');

			newForm.attr("name","newForm");
			newForm.attr("method","post");
			newForm.attr("action",'/order/complete');
			newForm.attr("target","_self");

			newForm.append($('<input/>', {type: 'hidden', name: 'id', value: id }));
			newForm.append($('<input/>', {type: 'hidden', name: [[${_csrf.parameterName}]], value:[[${_csrf.token}]]} ));

			$(document.body).append(newForm);

			newForm.submit();
		}

		fnMoveReservationView = function(id){
			var newForm = $('<form></form>');

			newForm.attr("name","newForm");
			newForm.attr("method","get");
			newForm.attr("action",`/member/reservation/view/${id}`);
			newForm.attr("target","_self");

			newForm.append($('<input/>', {type: 'hidden', name: 'id', value: id }));
			//newForm.append($('<input/>', {type: 'hidden', name: [[${_csrf.parameterName}]], value:[[${_csrf.token}]]} ));
			$(document.body).append(newForm);

			newForm.submit();
		}
	</script>

	<style>
		.order_area {padding-top: 80px;position: relative;}
		.order_area .order_menu { max-width: 1280px; margin: 0 auto; overflow: hidden; margin-bottom: 32px;}
		.order_area .order_area_title { overflow: hidden; /*margin-bottom: 32px;*/ }
		.order_area .order_area_title .more_box { display: block;  float: right; width: 86px; height: 33px; border: 1px solid #CCCCCC; box-sizing: border-box; border-radius: 16px; position: relative; top: -30px;}
		.order_area .order_area_title .more_box.active {display:inline-block;}
		.order_area .order_area_title .more_box:hover span {border-bottom: 1px solid #666666;cursor: pointer;}

		.order_area .order_area_grp {display: flex;justify-content: center;flex-direction: column;align-items: flex-start;font-weight: 400;}
		.order_area .order_area_txt {color:#222;font-size:40px;font-weight:600;line-height: 38px;}
		.order_area .order_area_desc {margin-top:43px; font-size:18px;color:#333;}
		.order_area .order_area_desc p{text-align: center;line-height: 28px;}

		.order_form {max-width: 1280px; display: flex;flex-direction: column; justify-content: center; margin: 84px auto }
		.order_form .title{ color: #222;font-size: 30px;font-style: normal;font-weight: 600;margin-bottom: 20px; }

		.order_form .control{ height: 35px; border-radius: 5px;border: 1px solid #AAA; background-color: #fff; width: 100%; }

		.order_area .product_list{ flex-direction: column; border-top: 1px solid #333;max-width: 900px;margin: 0 auto 80px auto; }
		.order_area .product_info{padding: 20px 0;margin: 20px auto 0 auto;width: 100%;border-bottom: 1px solid #ccc;}
		.order_area .alert_msg{margin: 20px auto 0 auto;width: 100%;display: flex;align-items: center;}

		.btn_box {display: flex;justify-content: center;}
		.btn_box .way-btn{width: 399px;height: 60px;font-size: 20px; margin-right: 10px;}
		.btn_box .way-btn.upd{width: 132px;height: 41px;font-size: 16px;font-weight: 400;}

		.detail_line{ width: 100%;border-bottom: 1px solid #000; margin: 15px 0;}
		.detail_line.gray{ border-color: #ccc;}
		.detail_form_box .detail_line{ width: 100%;border-bottom: 1px solid #ccc; margin-top: 11px;margin-bottom: 15px;}
		.detail_line.deshed{ border-bottom: 1px dashed #ccc;}

		.row{display: flex;}
		.row.col{flex-direction: column;}
		.row.aic{align-items: center;}
		.row.jcc{justify-content: center;}
		.row.jcfe,
		.btn_box.jcfe{justify-content: flex-end;}
		.row.jcse{justify-content: space-evenly;}

		.circle{width: 59px; height: 59px; border-radius:50%;display: flex;justify-content: center;align-items: center;}
		.circle.green{background-color: #47691F;}

		.item{padding: 5px 0;font-size: 18px;font-weight: 400;}
		.item label{font-weight: 600;}
		.item span{ padding:0 10px; }
		.item span:not(:last-child):after{content: '|';padding-left: 10px;}
		.item .reservater{border: 1px solid #965841;border-radius:3px; color: #965841;padding: 5px;}

		.product_info {padding-left: 80px;align-items: flex-start;max-width: 1200px;}
		.product_info .detail_desc_box {display: flex;flex-direction: column;justify-content: space-between;height: 100%;}
		.product_info .detail_line{margin: 20px 0;}

		.product_info {display: flex;align-items: center;/*margin:0 auto;*/padding-bottom: 30px;margin-bottom:30px;}
		.product_info img{max-width: 137px;max-height: 112px;object-fit: cover;}
		.product_chk {width: 22px;height: 22px;margin-right: 10px;}
		.product_text {width: calc(100% - 247px);padding: 0 29px;}
		.product_text p{line-height: 24px; font-size: 14px;font-weight: 400;padding-bottom: 10px;display: flex; align-items: center;}
		.product_text .product_title{font-size: 18px;font-weight: 600;line-height: 34px;}
		.product_link {display: flex;justify-content: center;align-items: center;flex-direction: column;;}
		.product_link .btn {width: 116px;height: 36px;border-radius: 50px;background: #fff;font-size: 15px;font-weight: 500;margin-bottom: 5px;cursor: pointer;}

	</style>

</body>
</html>